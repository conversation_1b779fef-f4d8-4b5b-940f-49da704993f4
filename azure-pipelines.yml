trigger:
- Master_FR002 # or the name of your main branch
- feature/*

pool:
  name: 'SonarLIMS'

steps:
 # Checkout the repository 
- checkout: self

 # Disable shallow fetch
  fetchDepth: 0

# Prepare Analysis Configuration task
- task: SonarQubePrepare@7
  inputs:
    SonarQube: 'Sonarqube'
    scannerMode: 'dotnet'
    projectKey: 'CaliberLIMS_CaliberLIMS_2df0cc5d-6cf4-4fd5-a7f5-1f2a3736b980'

# Dotnet build task
- task: DotNetCoreCLI@2
  displayName: 'dotnet build'

# Run Code Analysis task
- task: SonarQubeAnalyze@7

# Publish Quality Gate Result task
- task: SonarQubePublish@7
  inputs:
    pollingTimeoutSec: '300'