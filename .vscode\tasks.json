{"version": "2.0.0", "tasks": [{"label": "Build API", "type": "process", "command": "dotnet", "args": ["build", "${workspaceFolder}/Src/Services/CaliberLIMS.Api/CaliberLIMS.Api.csproj"], "problemMatcher": "$msCompile", "group": "build"}, {"label": "Build UI", "type": "process", "command": "dotnet", "args": ["build", "${workspaceFolder}/Src/CaliberLIMS.UI/CaliberLIMS.UI.csproj"], "problemMatcher": "$msCompile", "group": "build"}, {"label": "Open UI in Browser", "type": "shell", "command": "start https://localhost:5001", "windows": {"command": "start https://localhost:5001"}, "problemMatcher": []}]}